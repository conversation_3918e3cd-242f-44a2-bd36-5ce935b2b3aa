<!DOCTYPE html>
<html lang="pt">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Anchorpoint - Soluções Logísticas de Confiança</title>
    <style>
        /* Updated Color Palette - Anchorpoint Brand Colors */
        :root {
            --primary-navy: #1a237e;
            --primary-dark: #0d1654;
            --accent-pink: #e91e63;
            --accent-pink-light: #f48fb1;
            --white: #ffffff;
            --light-gray: #f5f7fa;
            --medium-gray: #64748b;
            --dark-gray: #1e293b;
            --text-primary: #0f172a;
            --text-secondary: #475569;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            overflow-x: hidden;
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            background: linear-gradient(135deg, var(--primary-navy) 0%, var(--primary-dark) 100%);
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23e91e63" stop-opacity="0.1"/><stop offset="100%" stop-color="%23e91e63" stop-opacity="0"/></radialGradient></defs><rect width="1000" height="1000" fill="url(%23a)"/></svg>') no-repeat center center;
            background-size: cover;
        }

        .navbar {
            position: fixed;
            top: 0;
            width: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            z-index: 1000;
            padding: 1rem 0;
            transition: all 0.3s ease;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 800;
            color: var(--primary-navy);
            text-decoration: none;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            text-decoration: none;
            color: var(--text-primary);
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: var(--accent-pink);
        }

        .hero-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            text-align: center;
            color: white;
            position: relative;
            z-index: 2;
        }

        .hero h1 {
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 800;
            margin-bottom: 1.5rem;
            line-height: 1.2;
        }

        .hero p {
            font-size: 1.25rem;
            margin-bottom: 2.5rem;
            opacity: 0.9;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .cta-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            cursor: pointer;
            display: inline-block;
        }

        .btn-primary {
            background: var(--accent-pink);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(233, 30, 99, 0.3);
            background: #c2185b;
        }

        .btn-secondary {
            background: transparent;
            color: white;
            border: 2px solid white;
        }

        .btn-secondary:hover {
            background: white;
            color: var(--primary-navy);
        }

        /* Services Section */
        .services {
            padding: 5rem 0;
            background: var(--light-gray);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .section-title {
            text-align: center;
            margin-bottom: 3rem;
        }

        .section-title h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 1rem;
        }

        .section-title p {
            font-size: 1.1rem;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        .section-title-white h2,
        .section-title-white p {
            color: var(--white);
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .service-card {
            background: white;
            padding: 2.5rem;
            border-radius: 20px;
            box-shadow: 0 10px 40px rgba(26, 35, 126, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-navy), var(--accent-pink));
        }

        .service-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 60px rgba(26, 35, 126, 0.15);
        }

        .service-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary-navy), var(--accent-pink));
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
            font-size: 1.5rem;
            color: white;
        }

        .service-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .service-card p {
            color: var(--text-secondary);
            line-height: 1.6;
        }

        /* Enhanced About Section Styles */
        .about {
            padding: 6rem 0;
            background: linear-gradient(135deg, var(--light-gray) 0%, #e2e8f0 100%);
            position: relative;
        }

        .about::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%231a237e" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="%23e91e63" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }

        .company-story {
            background: white;
            border-radius: 25px;
            padding: 3rem;
            margin: 3rem 0;
            box-shadow: 0 20px 60px rgba(26, 35, 126, 0.1);
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 3rem;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .company-story::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, var(--primary-navy), var(--accent-pink));
        }

        .story-content h3 {
            font-size: 2rem;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            font-weight: 700;
        }

        .story-content p {
            font-size: 1.1rem;
            line-height: 1.8;
            color: var(--text-secondary);
        }

        .story-stats {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .stat-item {
            text-align: center;
            padding: 1.5rem;
            background: linear-gradient(135deg, rgba(26, 35, 126, 0.1), rgba(233, 30, 99, 0.1));
            border-radius: 15px;
            transition: transform 0.3s ease;
        }

        .stat-item:hover {
            transform: scale(1.05);
        }

        .stat-number {
            display: block;
            font-size: 2rem;
            font-weight: 800;
            color: var(--primary-navy);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
            color: var(--text-secondary);
            font-weight: 600;
        }

        .values-section {
            margin: 4rem 0;
        }

        .values-title {
            text-align: center;
            font-size: 2.2rem;
            color: var(--text-primary);
            margin-bottom: 2.5rem;
            font-weight: 700;
        }

        .values-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
        }

        .value-card-enhanced {
            background: white;
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 15px 50px rgba(26, 35, 126, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .value-card-enhanced::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-navy), var(--accent-pink));
        }

        .value-card-enhanced:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 70px rgba(26, 35, 126, 0.15);
        }

        .value-icon {
            font-size: 3rem;
            margin-bottom: 1.5rem;
        }

        .value-card-enhanced h4 {
            font-size: 1.8rem;
            color: var(--text-primary);
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .value-card-enhanced p {
            font-size: 1.1rem;
            line-height: 1.7;
            color: var(--text-secondary);
        }

        .core-values {
            background: white;
            border-radius: 25px;
            padding: 3rem;
            margin: 4rem 0;
            box-shadow: 0 20px 60px rgba(26, 35, 126, 0.1);
        }

        .core-values h3 {
            text-align: center;
            font-size: 2.2rem;
            color: var(--text-primary);
            margin-bottom: 3rem;
            font-weight: 700;
        }

        .values-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }

        .value-pillar {
            text-align: center;
            padding: 2rem;
            border-radius: 15px;
            background: linear-gradient(135deg, rgba(26, 35, 126, 0.05), rgba(233, 30, 99, 0.05));
            transition: transform 0.3s ease;
            border: 1px solid rgba(26, 35, 126, 0.1);
        }

        .value-pillar:hover {
            transform: translateY(-5px);
            background: linear-gradient(135deg, rgba(26, 35, 126, 0.1), rgba(233, 30, 99, 0.1));
        }

        .pillar-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .value-pillar h5 {
            font-size: 1.4rem;
            color: var(--primary-navy);
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .value-pillar p {
            font-size: 1rem;
            line-height: 1.6;
            color: var(--text-secondary);
        }

        .objectives-section {
            background: linear-gradient(135deg, var(--primary-navy), var(--primary-dark));
            border-radius: 25px;
            padding: 4rem 3rem;
            color: white;
            margin-top: 4rem;
            position: relative;
            overflow: hidden;
        }

        .objectives-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="obj" cx="50%" cy="50%"><stop offset="0%" stop-color="%23e91e63" stop-opacity="0.1"/><stop offset="100%" stop-color="%23e91e63" stop-opacity="0"/></radialGradient></defs><rect width="1000" height="1000" fill="url(%23obj)"/></svg>') no-repeat center center;
            background-size: cover;
        }

        .objectives-content {
            position: relative;
            z-index: 2;
        }

        .objectives-content h3 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--accent-pink), var(--accent-pink-light));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .objectives-list {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .objective-item {
            display: flex;
            align-items: flex-start;
            gap: 2rem;
            background: rgba(255, 255, 255, 0.1);
            padding: 2rem;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }

        .objective-item:hover {
            transform: translateX(10px);
            background: rgba(255, 255, 255, 0.15);
        }

        .objective-number {
            font-size: 2rem;
            font-weight: 800;
            color: var(--accent-pink);
            min-width: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(233, 30, 99, 0.2);
            border-radius: 50%;
            width: 60px;
            height: 60px;
        }

        .objective-text h5 {
            font-size: 1.4rem;
            margin-bottom: 0.5rem;
            font-weight: 700;
        }

        .objective-text p {
            font-size: 1rem;
            line-height: 1.6;
            opacity: 0.9;
        }

        .value-pillar {
            text-align: center;
            padding: 2rem;
            border-radius: 15px;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
            transition: transform 0.3s ease;
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        .value-pillar:hover {
            transform: translateY(-5px);
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
        }

        .pillar-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .value-pillar h5 {
            font-size: 1.4rem;
            color: #667eea;
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .value-pillar p {
            font-size: 1rem;
            line-height: 1.6;
            color: #4a5568;
        }

        .objectives-section {
            background: linear-gradient(135deg, #1a1a1a, #333);
            border-radius: 25px;
            padding: 4rem 3rem;
            color: white;
            margin-top: 4rem;
            position: relative;
            overflow: hidden;
        }

        .objectives-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="obj" cx="50%" cy="50%"><stop offset="0%" stop-color="%23667eea" stop-opacity="0.1"/><stop offset="100%" stop-color="%23667eea" stop-opacity="0"/></radialGradient></defs><rect width="1000" height="1000" fill="url(%23obj)"/></svg>') no-repeat center center;
            background-size: cover;
        }

        .objectives-content {
            position: relative;
            z-index: 2;
        }

        .objectives-content h3 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-navy));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .objectives-list {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .objective-item {
            display: flex;
            align-items: flex-start;
            gap: 2rem;
            background: rgba(255, 255, 255, 0.1);
            padding: 2rem;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }

        .objective-item:hover {
            transform: translateX(10px);
            background: rgba(255, 255, 255, 0.15);
        }

        .objective-number {
            font-size: 2rem;
            font-weight: 800;
            color: #667eea;
            min-width: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(102, 126, 234, 0.2);
            border-radius: 50%;
            width: 60px;
            height: 60px;
        }

        .objective-text h5 {
            font-size: 1.4rem;
            margin-bottom: 0.5rem;
            font-weight: 700;
        }

        .objective-text p {
            font-size: 1rem;
            line-height: 1.6;
            opacity: 0.9;
        }

        /* Mobile responsiveness for enhanced about section */
        @media (max-width: 768px) {
            .company-story {
                grid-template-columns: 1fr;
                gap: 2rem;
                padding: 2rem;
            }

            .story-stats {
                flex-direction: row;
                justify-content: space-around;
            }

            .values-grid {
                grid-template-columns: 1fr;
            }

            .values-showcase {
                grid-template-columns: 1fr;
            }

            .objective-item {
                flex-direction: column;
                text-align: center;
                gap: 1rem;
            }

            .objectives-section {
                padding: 2rem 1.5rem;
            }
        }

        /* Enhanced Contact Section */
        .contact {
            padding: 6rem 0;
            background: linear-gradient(135deg, rgba(26, 35, 126) 0%, rgba(233, 30, 99) 100%);
            position: relative;
            color: white;
        }

        .contact::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="contact-bg" cx="30%" cy="40%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><rect width="1000" height="1000" fill="url(%23contact-bg)"/></svg>') no-repeat center center;
            background-size: cover;
        }

        .contact-content {
            position: relative;
            z-index: 2;
        }

        .contact-main {
            display: grid;
            grid-template-columns: 1fr 1.5fr;
            gap: 4rem;
            margin-top: 3rem;
        }

        .quick-contact {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .contact-card-new {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 2rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .contact-card-new::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.3));
        }

        .contact-card-new:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.25);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
        }

        .contact-icon {
            width: 50px;
            height: 50px;
            margin-bottom: 1rem;
            color: #ffffff;
        }

        .contact-icon svg {
            width: 100%;
            height: 100%;
        }

        .contact-card-new h4 {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: #ffffff;
        }

        .contact-card-new p {
            margin-bottom: 0.25rem;
            opacity: 0.9;
        }

        .phone-primary {
            font-weight: 600;
            font-size: 1.1rem;
        }

        .phone-secondary {
            opacity: 0.8;
        }

        .country {
            font-style: italic;
            opacity: 0.7;
        }

        .email-link {
            display: inline-block;
            margin-top: 0.5rem;
            padding: 0.5rem 1rem;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            text-decoration: none;
            color: white;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .email-link:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        /* Contact Form */
        .contact-form-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 3rem;
            backdrop-filter: blur(20px);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }

        .form-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .form-header h3 {
            font-size: 2rem;
            color: #1a1a1a;
            margin-bottom: 0.5rem;
            font-weight: 700;
        }

        .form-header p {
            color: #666;
            font-size: 1rem;
        }

        .contact-form {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            color: #333;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 0.75rem 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .submit-btn {
            background: linear-gradient(135deg, rgba(26, 35, 126), rgba(233, 30, 99));
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .submit-btn svg {
            width: 20px;
            height: 20px;
        }

        /* Additional Contact Info */
        .contact-additional {
            display: flex;
            justify-content: space-around;
            margin-top: 3rem;
            gap: 2rem;
        }

        .response-time,
        .availability {
            display: flex;
            align-items: center;
            gap: 1rem;
            background: rgba(255, 255, 255, 0.1);
            padding: 1.5rem;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            flex: 1;
        }

        .time-icon,
        .avail-icon {
            font-size: 2rem;
        }

        .time-content h5,
        .avail-content h5 {
            font-size: 1.1rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .time-content p,
        .avail-content p {
            font-size: 0.9rem;
            opacity: 0.9;
            margin: 0;
        }

        /* Enhanced Footer */
        .footer {
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%);
            color: white;
            position: relative;
            overflow: hidden;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="footer-pattern" width="50" height="50" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23667eea" opacity="0.1"/></pattern></defs><rect width="1000" height="1000" fill="url(%23footer-pattern)"/></svg>');
        }

        .footer-content {
            position: relative;
            z-index: 2;
            padding: 4rem 0 0 0;
        }

        .footer-main {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1.5fr;
            gap: 3rem;
            margin-bottom: 3rem;
        }

        .footer-section h4 {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #667eea;
        }

        .footer-logo h3 {
            font-size: 2rem;
            font-weight: 800;
            color: #667eea;
            margin-bottom: 0.5rem;
        }

        .logo-tagline {
            font-size: 0.9rem;
            color: #94a3b8;
            margin-bottom: 1rem;
        }

        .company-description {
            line-height: 1.6;
            color: #cbd5e1;
            margin-bottom: 1.5rem;
        }

        .footer-certifications {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .cert-badge {
            background: rgba(102, 126, 234, 0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            color: #667eea;
            border: 1px solid rgba(102, 126, 234, 0.3);
            width: fit-content;
        }

        .footer-links {
            list-style: none;
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .footer-links a {
            color: #cbd5e1;
            text-decoration: none;
            transition: color 0.3s ease;
            font-size: 0.95rem;
        }

        .footer-links a:hover {
            color: #667eea;
        }

        .footer-contact {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .contact-item {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .contact-label {
            font-size: 0.8rem;
            color: #94a3b8;
        }

        .contact-item span:last-child {
            color: #cbd5e1;
            font-size: 0.95rem;
        }

        /* Footer Bottom */
        .footer-bottom {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding: 2rem 0;
        }

        .footer-bottom-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .copyright p {
            color: #94a3b8;
            font-size: 0.9rem;
        }

        .footer-links-bottom {
            display: flex;
            gap: 2rem;
        }

        .footer-links-bottom a {
            color: #cbd5e1;
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }

        .footer-links-bottom a:hover {
            color: #667eea;
        }

        .made-with-love {
            color: #94a3b8;
            font-size: 0.9rem;
        }

        /* Mobile Responsiveness */
        @media (max-width: 768px) {
            .contact-main {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .contact-additional {
                flex-direction: column;
            }

            .footer-main {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .footer-bottom-content {
                flex-direction: column;
                text-align: center;
            }

            .footer-links-bottom {
                flex-wrap: wrap;
                justify-content: center;
            }

            .contact-form-section {
                padding: 2rem;
            }
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.8s ease-out;
        }

        /* Mobile Responsiveness */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .hero h1 {
                font-size: 2.5rem;
            }

            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }

            .services-grid,
            .about-grid,
            .contact-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Loading animation */
        .loading-dot {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: currentColor;
            margin: 0 2px;
            animation: loading 1.4s ease-in-out infinite both;
        }

        .loading-dot:nth-child(1) {
            animation-delay: -0.32s;
        }

        .loading-dot:nth-child(2) {
            animation-delay: -0.16s;
        }

        @keyframes loading {

            0%,
            80%,
            100% {
                transform: scale(0);
            }

            40% {
                transform: scale(1);
            }
        }
    </style>
</head>

<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="#" class="logo">Anchorpoint</a>
            <ul class="nav-links">
                <li><a href="#home">Início</a></li>
                <li><a href="#services">Serviços</a></li>
                <li><a href="#about">Sobre</a></li>
                <li><a href="#contact">Contacto</a></li>
            </ul>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-content fade-in-up">
            <h1>Soluções Logísticas de Confiança</h1>
            <p>Fornecemos conveniência e tranquilidade aos nossos clientes através de soluções logísticas
                personalizadas, entregando produtos essenciais de forma oportuna e agregando valor.</p>
            <div class="cta-buttons">
                <a href="#services" class="btn btn-primary">Nossos Serviços</a>
                <a href="#contact" class="btn btn-secondary">Fale Connosco</a>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="services">
        <div class="container">
            <div class="section-title fade-in-up">
                <h2>Nossos Serviços</h2>
                <p>Oferecemos soluções completas para as suas necessidades logísticas e de produtos essenciais</p>
            </div>

            <div class="services-grid">
                <div class="service-card fade-in-up">
                    <div class="service-icon">✈️</div>
                    <h3>Air Charter</h3>
                    <p>Aluguer de transporte aéreo para entrega de produtos essenciais, garantindo rapidez e segurança
                        na entrega dos seus produtos mais importantes.</p>
                </div>

                <div class="service-card fade-in-up">
                    <div class="service-icon">✅</div>
                    <h3>Quality Assurance</h3>
                    <p>Garantia de qualidade dos produtos adquiridos até à entrega, assegurando que recebe exactamente o
                        que precisa, nas condições ideais.</p>
                </div>

                <div class="service-card fade-in-up">
                    <div class="service-icon">📦</div>
                    <h3>Universal Essentials</h3>
                    <p>Fornecimento de produtos essenciais incluindo carne, material de escritório e outros bens
                        fundamentais para o funcionamento do seu negócio.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about">
        <div class="container">
            <div class="section-title fade-in-up">
                <h2>Conheça a Anchorpoint</h2>
                <p>Mais do que uma empresa de logística - somos o seu parceiro estratégico para o sucesso</p>
            </div>

            <!-- Company Story -->
            <div class="company-story fade-in-up">
                <div class="story-content">
                    <h3>A Nossa História</h3>
                    <p>Nascemos da necessidade de simplificar a complexidade logística em Moçambique. Entendemos que
                        cada minuto perdido em processos logísticos é um minuto a menos focado no crescimento do seu
                        negócio. Por isso, criámos soluções que não apenas entregam produtos, mas entregam
                        <strong>tranquilidade</strong>.
                    </p>
                </div>
                <div class="story-stats">
                    <div class="stat-item">
                        <span class="stat-number">100%</span>
                        <span class="stat-label">Satisfação do Cliente</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">24/7</span>
                        <span class="stat-label">Disponibilidade</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">Premium</span>
                        <span class="stat-label">Qualidade Garantida</span>
                    </div>
                </div>
            </div>

            <!-- Values Grid -->
            <div class="values-section">
                <h3 class="values-title fade-in-up">Os Nossos Pilares</h3>
                <div class="values-grid">
                    <div class="value-card-enhanced fade-in-up">
                        <div class="value-icon">🎯</div>
                        <h4>Visão</h4>
                        <p>Ser reconhecidos como <strong>o parceiro logístico mais confiável</strong> em Moçambique,
                            estabelecendo novos padrões de excelência na entrega de produtos essenciais.</p>
                    </div>

                    <div class="value-card-enhanced fade-in-up">
                        <div class="value-icon">🚀</div>
                        <h4>Missão</h4>
                        <p>Transformar desafios logísticos em <strong>oportunidades de crescimento</strong>, oferecendo
                            soluções personalizadas que permitem aos nossos clientes concentrar-se no que fazem melhor.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Core Values -->
            <div class="core-values fade-in-up">
                <h3>Valores que Nos Definem</h3>
                <div class="values-showcase">
                    <div class="value-pillar">
                        <div class="pillar-icon">🤝</div>
                        <h5>Respeito</h5>
                        <p>Tratamos cada cliente, fornecedor e colaborador com dignidade e consideração, valorizando a
                            diversidade e promovendo relações baseadas na confiança mútua.</p>
                    </div>
                    <div class="value-pillar">
                        <div class="pillar-icon">💪</div>
                        <h5>Compromisso</h5>
                        <p>Cumprimos as nossas promessas sem excepções. Quando assumimos uma responsabilidade,
                            mobilizamos todos os recursos necessários para entregar resultados excepcionais.</p>
                    </div>
                    <div class="value-pillar">
                        <div class="pillar-icon">✨</div>
                        <h5>Honestidade</h5>
                        <p>A transparência é a base de todas as nossas relações. Comunicamos com clareza, admitimos os
                            nossos erros e trabalhamos incansavelmente para os corrigir.</p>
                    </div>
                </div>
            </div>

            <!-- Objectives Section -->
            <div class="objectives-section fade-in-up">
                <div class="objectives-content">
                    <h3>O Que Nos Move</h3>
                    <div class="objectives-list">
                        <div class="objective-item">
                            <div class="objective-number">01</div>
                            <div class="objective-text">
                                <h5>Excelência Operacional</h5>
                                <p>Estabelecer novos padrões de qualidade na entrega de produtos essenciais, eliminando
                                    os pontos de dor logística dos nossos clientes.</p>
                            </div>
                        </div>
                        <div class="objective-item">
                            <div class="objective-number">02</div>
                            <div class="objective-text">
                                <h5>Inovação Contínua</h5>
                                <p>Desenvolver constantemente novas soluções que antecipem as necessidades do mercado e
                                    superem as expectativas dos clientes.</p>
                            </div>
                        </div>
                        <div class="objective-item">
                            <div class="objective-number">03</div>
                            <div class="objective-text">
                                <h5>Impacto Positivo</h5>
                                <p>Contribuir para o desenvolvimento económico de Moçambique, criando valor para todos
                                    os stakeholders envolvidos.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact">
        <div class="container">
            <div class="section-title section-title-white fade-in-up">
                <h2>Vamos Trabalhar Juntos</h2>
                <p>Transforme os seus desafios logísticos em oportunidades de crescimento. Fale connosco hoje!</p>
            </div>

            <div class="contact-content">
                <!-- Contact Form & Info Grid -->
                <div class="contact-main">
                    <!-- Quick Contact Cards -->
                    <div class="quick-contact fade-in-up">
                        <div class="contact-card-new">
                            <div class="contact-icon">
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path
                                        d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z" />
                                </svg>
                            </div>
                            <h4>Localização</h4>
                            <p>Maputo, Cidade de Maputo</p>
                            <p class="country">Moçambique</p>
                        </div>

                        <div class="contact-card-new">
                            <div class="contact-icon">
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path
                                        d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z" />
                                </svg>
                            </div>
                            <h4>Telefones</h4>
                            <p class="phone-primary">+258 840 600 224</p>
                            <p class="phone-secondary">+258 853 300 201</p>
                        </div>

                        <div class="contact-card-new">
                            <div class="contact-icon">
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path
                                        d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z" />
                                </svg>
                            </div>
                            <h4>Email</h4>
                            <p><EMAIL></p>
                            <a href="mailto:<EMAIL>" class="email-link">Enviar Email</a>
                        </div>
                    </div>

                    <!-- Contact Form -->
                    <div class="contact-form-section fade-in-up">
                        <div class="form-header">
                            <h3>Solicite uma Cotação</h3>
                            <p>Descreva as suas necessidades e entraremos em contacto em menos de 24 horas</p>
                        </div>

                        <form class="contact-form" id="contactForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="name">Nome Completo</label>
                                    <input type="text" id="name" name="name" required>
                                </div>
                                <div class="form-group">
                                    <label for="company">Empresa</label>
                                    <input type="text" id="company" name="company">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="email">Email</label>
                                    <input type="email" id="email" name="email" required>
                                </div>
                                <div class="form-group">
                                    <label for="phone">Telefone</label>
                                    <input type="tel" id="phone" name="phone">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="service">Serviço de Interesse</label>
                                <select id="service" name="service" required>
                                    <option value="">Selecione um serviço</option>
                                    <option value="air-charter">Air Charter</option>
                                    <option value="quality-assurance">Quality Assurance</option>
                                    <option value="universal-essentials">Universal Essentials</option>
                                    <option value="consultation">Consultoria Logística</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="message">Descreva as suas necessidades</label>
                                <textarea id="message" name="message" rows="4"
                                    placeholder="Conte-nos sobre o seu projeto ou necessidades logísticas..."></textarea>
                            </div>

                            <button type="submit" class="submit-btn">
                                <span>Enviar Pedido</span>
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z" />
                                </svg>
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Additional Info -->
                <div class="contact-additional fade-in-up">
                    <div class="response-time">
                        <div class="time-icon">⚡</div>
                        <div class="time-content">
                            <h5>Resposta Rápida</h5>
                            <p>Respondemos a todos os pedidos em menos de 24 horas</p>
                        </div>
                    </div>

                    <div class="availability">
                        <div class="avail-icon">🕒</div>
                        <div class="avail-content">
                            <h5>Disponibilidade 24/7</h5>
                            <p>Para emergências logísticas, estamos sempre disponíveis</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <div class="container">
                <!-- Main Footer Content -->
                <div class="footer-main">
                    <!-- Company Info -->
                    <div class="footer-section company-info">
                        <div class="footer-logo">
                            <h3>Anchorpoint</h3>
                            <div class="logo-tagline">Soluções Logísticas de Confiança</div>
                        </div>
                        <p class="company-description">
                            Transformamos desafios logísticos em oportunidades de crescimento,
                            oferecendo soluções personalizadas que permitem aos nossos clientes
                            concentrar-se no que fazem melhor.
                        </p>
                        <div class="footer-certifications">
                            <span class="cert-badge">🏆 Qualidade Certificada</span>
                            <span class="cert-badge">✈️ Transporte Aéreo</span>
                        </div>
                    </div>

                    <!-- Quick Links -->
                    <div class="footer-section">
                        <h4>Navegação</h4>
                        <ul class="footer-links">
                            <li><a href="#home">Início</a></li>
                            <li><a href="#services">Serviços</a></li>
                            <li><a href="#about">Sobre Nós</a></li>
                            <li><a href="#contact">Contacto</a></li>
                        </ul>
                    </div>

                    <!-- Services -->
                    <div class="footer-section">
                        <h4>Nossos Serviços</h4>
                        <ul class="footer-links">
                            <li><a href="#services">Air Charter</a></li>
                            <li><a href="#services">Quality Assurance</a></li>
                            <li><a href="#services">Universal Essentials</a></li>
                            <li><a href="#contact">Consultoria Logística</a></li>
                        </ul>
                    </div>

                    <!-- Contact Info -->
                    <div class="footer-section">
                        <h4>Contacto</h4>
                        <div class="footer-contact">
                            <div class="contact-item">
                                <span class="contact-label">📍 Localização:</span>
                                <span>Maputo, Moçambique</span>
                            </div>
                            <div class="contact-item">
                                <span class="contact-label">📞 Telefone:</span>
                                <span>+258 840 600 224</span>
                            </div>
                            <div class="contact-item">
                                <span class="contact-label">✉️ Email:</span>
                                <span><EMAIL></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Footer Bottom -->
                <div class="footer-bottom">
                    <div class="footer-bottom-content">
                        <div class="copyright">
                            <p>&copy; 2025 Anchorpoint. Todos os direitos reservados.</p>
                        </div>
                        <div class="footer-links-bottom">
                            <a href="#privacy">Política de Privacidade</a>
                            <a href="#terms">Termos de Serviço</a>
                            <a href="#cookies">Cookies</a>
                        </div>
                        <div class="made-with-love">
                            <span>Feito com ❤️ em Moçambique</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all fade-in-up elements
        document.querySelectorAll('.fade-in-up').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(30px)';
            el.style.transition = 'all 0.8s ease-out';
            observer.observe(el);
        });

        // Navbar background on scroll
        window.addEventListener('scroll', () => {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.style.background = 'rgba(255, 255, 255, 0.98)';
                navbar.style.boxShadow = '0 2px 20px rgba(0,0,0,0.1)';
            } else {
                navbar.style.background = 'rgba(255, 255, 255, 0.95)';
                navbar.style.boxShadow = 'none';
            }
        });

        // Enhanced form functionality
        document.getElementById('contactForm').addEventListener('submit', function (e) {
            e.preventDefault();

            const submitBtn = this.querySelector('.submit-btn');
            const originalText = submitBtn.innerHTML;

            // Show loading state
            submitBtn.innerHTML = `
                <span>Enviando...</span>
                <div class="loading-spinner"></div>
            `;
            submitBtn.disabled = true;

            // Simulate form submission
            setTimeout(() => {
                submitBtn.innerHTML = `
                    <span>✓ Enviado com Sucesso!</span>
                `;
                submitBtn.style.background = 'linear-gradient(135deg, #10b981, #059669)';

                // Reset after 3 seconds
                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                    submitBtn.style.background = 'linear-gradient(135deg, #667eea, #764ba2)';
                    this.reset();
                }, 3000);
            }, 2000);
        });

        // Add loading spinner CSS
        const loadingSpinnerCSS = `
            .loading-spinner {
                width: 20px;
                height: 20px;
                border: 2px solid rgba(255,255,255,0.3);
                border-radius: 50%;
                border-top-color: white;
                animation: spin 1s ease-in-out infinite;
            }
            
            @keyframes spin {
                to { transform: rotate(360deg); }
            }
        `;

        // Inject the CSS
        const style = document.createElement('style');
        style.textContent = loadingSpinnerCSS;
        document.head.appendChild(style);
    </script>
</body>

</html>