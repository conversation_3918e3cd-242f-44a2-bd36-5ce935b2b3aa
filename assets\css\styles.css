/* Updated Color Palette - Anchorpoint Brand Colors */
:root {
  --primary-navy: #1a237e;
  --primary-dark: #0d1654;
  --accent-pink: #e91e63;
  --accent-pink-light: #f48fb1;
  --white: #ffffff;
  --light-gray: #f5f7fa;
  --medium-gray: #64748b;
  --dark-gray: #1e293b;
  --text-primary: #0f172a;
  --text-secondary: #475569;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  overflow-x: hidden;
}

/* Hero Section */
.hero {
  min-height: 100vh;
  background: linear-gradient(
    135deg,
    var(--primary-navy) 0%,
    var(--primary-dark) 100%
  );
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23e91e63" stop-opacity="0.1"/><stop offset="100%" stop-color="%23e91e63" stop-opacity="0"/></radialGradient></defs><rect width="1000" height="1000" fill="url(%23a)"/></svg>')
    no-repeat center center;
  background-size: cover;
}

.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  z-index: 1000;
  padding: 1rem 0;
  transition: all 0.3s ease;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 2rem;
}

.logo {
  font-size: 1.8rem;
  font-weight: 800;
  color: var(--primary-navy);
  text-decoration: none;
}

.nav-links {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-links a {
  text-decoration: none;
  color: var(--text-primary);
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-links a:hover {
  color: var(--accent-pink);
}

/* Language Selector */
.language-selector {
  position: relative;
  margin-left: 1rem;
}

.language-toggle {
  background: var(--primary-navy);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.language-toggle:hover {
  background: var(--accent-pink);
  transform: translateY(-2px);
}

.language-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  padding: 0.5rem 0;
  min-width: 120px;
  z-index: 1001;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
}

.language-dropdown.active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.language-option {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.3s ease;
  font-size: 0.9rem;
}

.language-option:hover {
  background: var(--light-gray);
}

.language-option.active {
  background: var(--primary-navy);
  color: white;
}

.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: center;
  color: white;
  position: relative;
  z-index: 2;
}

.hero h1 {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.hero p {
  font-size: 1.25rem;
  margin-bottom: 2.5rem;
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.btn {
  padding: 1rem 2rem;
  border: none;
  border-radius: 50px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  display: inline-block;
}

.btn-primary {
  background: var(--accent-pink);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(233, 30, 99, 0.3);
  background: #c2185b;
}

.btn-secondary {
  background: transparent;
  color: white;
  border: 2px solid white;
}

.btn-secondary:hover {
  background: white;
  color: var(--primary-navy);
}

/* Services Section */
.services {
  padding: 5rem 0;
  background: var(--light-gray);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.section-title {
  text-align: center;
  margin-bottom: 3rem;
}

.section-title h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.section-title p {
  font-size: 1.1rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

.section-title-white h2,
.section-title-white p {
  color: var(--white);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.service-card {
  background: white;
  padding: 2.5rem;
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(26, 35, 126, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-navy), var(--accent-pink));
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 60px rgba(26, 35, 126, 0.15);
}

.service-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-navy), var(--accent-pink));
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  color: white;
}

.service-card h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.service-card p {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Enhanced About Section Styles */
.about {
  padding: 6rem 0;
  background: linear-gradient(135deg, var(--light-gray) 0%, #e2e8f0 100%);
  position: relative;
}

.about::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%231a237e" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="%23e91e63" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
}

.company-story {
  background: white;
  border-radius: 25px;
  padding: 3rem;
  margin: 3rem 0;
  box-shadow: 0 20px 60px rgba(26, 35, 126, 0.1);
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 3rem;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.company-story::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(90deg, var(--primary-navy), var(--accent-pink));
}

.story-content h3 {
  font-size: 2rem;
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  font-weight: 700;
}

.story-content p {
  font-size: 1.1rem;
  line-height: 1.8;
  color: var(--text-secondary);
}

.story-stats {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.stat-item {
  text-align: center;
  padding: 1.5rem;
  background: linear-gradient(
    135deg,
    rgba(26, 35, 126, 0.1),
    rgba(233, 30, 99, 0.1)
  );
  border-radius: 15px;
  transition: transform 0.3s ease;
}

.stat-item:hover {
  transform: scale(1.05);
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 800;
  color: var(--primary-navy);
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-weight: 600;
}

.values-section {
  margin: 4rem 0;
}

.values-title {
  text-align: center;
  font-size: 2.2rem;
  color: var(--text-primary);
  margin-bottom: 2.5rem;
  font-weight: 700;
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.value-card-enhanced {
  background: white;
  padding: 3rem;
  border-radius: 20px;
  box-shadow: 0 15px 50px rgba(26, 35, 126, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.value-card-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-navy), var(--accent-pink));
}

.value-card-enhanced:hover {
  transform: translateY(-10px);
  box-shadow: 0 25px 70px rgba(26, 35, 126, 0.15);
}

.value-icon {
  font-size: 3rem;
  margin-bottom: 1.5rem;
}

.value-card-enhanced h4 {
  font-size: 1.8rem;
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-weight: 700;
}

.value-card-enhanced p {
  font-size: 1.1rem;
  line-height: 1.7;
  color: var(--text-secondary);
}

.core-values {
  background: white;
  border-radius: 25px;
  padding: 3rem;
  margin: 4rem 0;
  box-shadow: 0 20px 60px rgba(26, 35, 126, 0.1);
}

.core-values h3 {
  text-align: center;
  font-size: 2.2rem;
  color: var(--text-primary);
  margin-bottom: 3rem;
  font-weight: 700;
}

.values-showcase {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.value-pillar {
  text-align: center;
  padding: 2rem;
  border-radius: 15px;
  background: linear-gradient(
    135deg,
    rgba(26, 35, 126, 0.05),
    rgba(233, 30, 99, 0.05)
  );
  transition: transform 0.3s ease;
  border: 1px solid rgba(26, 35, 126, 0.1);
}

.value-pillar:hover {
  transform: translateY(-5px);
  background: linear-gradient(
    135deg,
    rgba(26, 35, 126, 0.1),
    rgba(233, 30, 99, 0.1)
  );
}

.pillar-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.value-pillar h5 {
  font-size: 1.4rem;
  color: var(--primary-navy);
  margin-bottom: 1rem;
  font-weight: 700;
}

.value-pillar p {
  font-size: 1rem;
  line-height: 1.6;
  color: var(--text-secondary);
}

.objectives-section {
  background: linear-gradient(135deg, var(--primary-navy), var(--primary-dark));
  border-radius: 25px;
  padding: 4rem 3rem;
  color: white;
  margin-top: 4rem;
  position: relative;
  overflow: hidden;
}

.objectives-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="obj" cx="50%" cy="50%"><stop offset="0%" stop-color="%23e91e63" stop-opacity="0.1"/><stop offset="100%" stop-color="%23e91e63" stop-opacity="0"/></radialGradient></defs><rect width="1000" height="1000" fill="url(%23obj)"/></svg>')
    no-repeat center center;
  background-size: cover;
}

.objectives-content {
  position: relative;
  z-index: 2;
}

.objectives-content h3 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  font-weight: 700;
  background: linear-gradient(
    135deg,
    var(--accent-pink),
    var(--accent-pink-light)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.objectives-list {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.objective-item {
  display: flex;
  align-items: flex-start;
  gap: 2rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: transform 0.3s ease;
}

.objective-item:hover {
  transform: translateX(10px);
  background: rgba(255, 255, 255, 0.15);
}

.objective-number {
  font-size: 2rem;
  font-weight: 800;
  color: var(--accent-pink);
  min-width: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(233, 30, 99, 0.2);
  border-radius: 50%;
  width: 60px;
  height: 60px;
}

.objective-text h5 {
  font-size: 1.4rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.objective-text p {
  font-size: 1rem;
  line-height: 1.6;
  opacity: 0.9;
}

/* Enhanced Contact Section */
.contact {
  padding: 6rem 0;
  background: linear-gradient(
    135deg,
    rgba(26, 35, 126) 0%,
    rgba(233, 30, 99) 100%
  );
  position: relative;
  color: white;
}

.contact::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="contact-bg" cx="30%" cy="40%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><rect width="1000" height="1000" fill="url(%23contact-bg)"/></svg>')
    no-repeat center center;
  background-size: cover;
}

.contact-content {
  position: relative;
  z-index: 2;
}

.contact-main {
  display: grid;
  grid-template-columns: 1fr 1.5fr;
  gap: 4rem;
  margin-top: 3rem;
}

.quick-contact {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.contact-card-new {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.contact-card-new::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.8),
    rgba(255, 255, 255, 0.3)
  );
}

.contact-card-new:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.25);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.contact-icon {
  width: 50px;
  height: 50px;
  margin-bottom: 1rem;
  color: #ffffff;
}

.contact-icon svg {
  width: 100%;
  height: 100%;
}

.contact-card-new h4 {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #ffffff;
}

.contact-card-new p {
  margin-bottom: 0.25rem;
  opacity: 0.9;
}

.phone-primary {
  font-weight: 600;
  font-size: 1.1rem;
}

.phone-secondary {
  opacity: 0.8;
}

.country {
  font-style: italic;
  opacity: 0.7;
}

.email-link {
  display: inline-block;
  margin-top: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  text-decoration: none;
  color: white;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.email-link:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

/* Contact Form */
.contact-form-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 25px;
  padding: 3rem;
  backdrop-filter: blur(20px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.form-header {
  text-align: center;
  margin-bottom: 2rem;
}

.form-header h3 {
  font-size: 2rem;
  color: #1a1a1a;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.form-header p {
  color: #666;
  font-size: 1rem;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 0.75rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.submit-btn {
  background: linear-gradient(135deg, rgba(26, 35, 126), rgba(233, 30, 99));
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1rem;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.submit-btn svg {
  width: 20px;
  height: 20px;
}

/* Additional Contact Info */
.contact-additional {
  display: flex;
  justify-content: space-around;
  margin-top: 3rem;
  gap: 2rem;
}

.response-time,
.availability {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 1.5rem;
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  flex: 1;
}

.time-icon,
.avail-icon {
  font-size: 2rem;
}

.time-content h5,
.avail-content h5 {
  font-size: 1.1rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.time-content p,
.avail-content p {
  font-size: 0.9rem;
  opacity: 0.9;
  margin: 0;
}

/* Enhanced Footer */
.footer {
  background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="footer-pattern" width="50" height="50" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23667eea" opacity="0.1"/></pattern></defs><rect width="1000" height="1000" fill="url(%23footer-pattern)"/></svg>');
}

.footer-content {
  position: relative;
  z-index: 2;
  padding: 4rem 0 0 0;
}

.footer-main {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.5fr;
  gap: 3rem;
  margin-bottom: 3rem;
}

.footer-section h4 {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: #667eea;
}

.footer-logo h3 {
  font-size: 2rem;
  font-weight: 800;
  color: #667eea;
  margin-bottom: 0.5rem;
}

.logo-tagline {
  font-size: 0.9rem;
  color: #94a3b8;
  margin-bottom: 1rem;
}

.company-description {
  line-height: 1.6;
  color: #cbd5e1;
  margin-bottom: 1.5rem;
}

.footer-certifications {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.cert-badge {
  background: rgba(102, 126, 234, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  color: #667eea;
  border: 1px solid rgba(102, 126, 234, 0.3);
  width: fit-content;
}

.footer-links {
  list-style: none;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.footer-links a {
  color: #cbd5e1;
  text-decoration: none;
  transition: color 0.3s ease;
  font-size: 0.95rem;
}

.footer-links a:hover {
  color: #667eea;
}

.footer-contact {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.contact-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.contact-label {
  font-size: 0.8rem;
  color: #94a3b8;
}

.contact-item span:last-child {
  color: #cbd5e1;
  font-size: 0.95rem;
}

/* Footer Bottom */
.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 2rem 0;
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.copyright p {
  color: #94a3b8;
  font-size: 0.9rem;
}

.footer-links-bottom {
  display: flex;
  gap: 2rem;
}

.footer-links-bottom a {
  color: #cbd5e1;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.footer-links-bottom a:hover {
  color: #667eea;
}

.made-with-love {
  color: #94a3b8;
  font-size: 0.9rem;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.8s ease-out;
}

/* Loading animation */
.loading-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
  margin: 0 2px;
  animation: loading 1.4s ease-in-out infinite both;
}

.loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes loading {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .nav-links {
    display: none;
  }

  .hero h1 {
    font-size: 2.5rem;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .services-grid,
  .about-grid,
  .contact-grid {
    grid-template-columns: 1fr;
  }

  .company-story {
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 2rem;
  }

  .story-stats {
    flex-direction: row;
    justify-content: space-around;
  }

  .values-grid {
    grid-template-columns: 1fr;
  }

  .values-showcase {
    grid-template-columns: 1fr;
  }

  .objective-item {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .objectives-section {
    padding: 2rem 1.5rem;
  }

  .contact-main {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .contact-additional {
    flex-direction: column;
  }

  .footer-main {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .footer-bottom-content {
    flex-direction: column;
    text-align: center;
  }

  .footer-links-bottom {
    flex-wrap: wrap;
    justify-content: center;
  }

  .contact-form-section {
    padding: 2rem;
  }

  .language-selector {
    margin-left: 0.5rem;
  }

  .language-toggle {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }
}
