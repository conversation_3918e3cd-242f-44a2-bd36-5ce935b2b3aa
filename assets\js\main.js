// Anchorpoint Website - Main JavaScript
class AnchorpointApp {
    constructor() {
        this.currentLanguage = localStorage.getItem('anchorpoint-language') || 'pt';
        this.translations = {};
        this.init();
    }

    async init() {
        await this.loadTranslations();
        this.setupEventListeners();
        this.updateContent();
        this.setupAnimations();
    }

    async loadTranslations() {
        try {
            // Load Portuguese translations
            const ptResponse = await fetch('./assets/translations/pt.json');
            this.translations.pt = await ptResponse.json();

            // Load English translations
            const enResponse = await fetch('./assets/translations/en.json');
            this.translations.en = await enResponse.json();
        } catch (error) {
            console.error('Error loading translations:', error);
            // Fallback to default content if translations fail to load
        }
    }

    setupEventListeners() {
        // Language toggle
        const languageToggle = document.getElementById('languageToggle');
        const languageDropdown = document.getElementById('languageDropdown');
        const languageOptions = document.querySelectorAll('.language-option');

        if (languageToggle && languageDropdown) {
            languageToggle.addEventListener('click', (e) => {
                e.stopPropagation();
                languageDropdown.classList.toggle('active');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', () => {
                languageDropdown.classList.remove('active');
            });
        }

        // Language option selection
        languageOptions.forEach(option => {
            option.addEventListener('click', (e) => {
                e.preventDefault();
                const selectedLang = option.dataset.lang;
                this.changeLanguage(selectedLang);
                languageDropdown.classList.remove('active');
            });
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Contact form submission
        const contactForm = document.getElementById('contactForm');
        if (contactForm) {
            contactForm.addEventListener('submit', this.handleFormSubmission.bind(this));
        }

        // Navbar scroll effect
        window.addEventListener('scroll', this.handleNavbarScroll);
    }

    changeLanguage(lang) {
        if (this.translations[lang]) {
            this.currentLanguage = lang;
            localStorage.setItem('anchorpoint-language', lang);
            this.updateContent();
            this.updateLanguageToggle();
            
            // Update page meta information
            this.updateMetaTags();
        }
    }

    updateContent() {
        const t = this.translations[this.currentLanguage];
        if (!t) return;

        // Update navigation
        this.updateElement('[data-translate="nav.home"]', t.navigation.home);
        this.updateElement('[data-translate="nav.services"]', t.navigation.services);
        this.updateElement('[data-translate="nav.about"]', t.navigation.about);
        this.updateElement('[data-translate="nav.contact"]', t.navigation.contact);

        // Update hero section
        this.updateElement('[data-translate="hero.title"]', t.hero.title);
        this.updateElement('[data-translate="hero.subtitle"]', t.hero.subtitle);
        this.updateElement('[data-translate="hero.cta_primary"]', t.hero.cta_primary);
        this.updateElement('[data-translate="hero.cta_secondary"]', t.hero.cta_secondary);

        // Update services section
        this.updateElement('[data-translate="services.title"]', t.services.title);
        this.updateElement('[data-translate="services.subtitle"]', t.services.subtitle);
        this.updateElement('[data-translate="services.air_charter.title"]', t.services.air_charter.title);
        this.updateElement('[data-translate="services.air_charter.description"]', t.services.air_charter.description);
        this.updateElement('[data-translate="services.quality_assurance.title"]', t.services.quality_assurance.title);
        this.updateElement('[data-translate="services.quality_assurance.description"]', t.services.quality_assurance.description);
        this.updateElement('[data-translate="services.universal_essentials.title"]', t.services.universal_essentials.title);
        this.updateElement('[data-translate="services.universal_essentials.description"]', t.services.universal_essentials.description);

        // Update about section
        this.updateElement('[data-translate="about.title"]', t.about.title);
        this.updateElement('[data-translate="about.subtitle"]', t.about.subtitle);
        this.updateElement('[data-translate="about.story.title"]', t.about.story.title);
        this.updateElement('[data-translate="about.story.content"]', t.about.story.content, true);
        
        // Update stats
        this.updateElement('[data-translate="about.stats.satisfaction"]', t.about.stats.satisfaction);
        this.updateElement('[data-translate="about.stats.satisfaction_label"]', t.about.stats.satisfaction_label);
        this.updateElement('[data-translate="about.stats.availability"]', t.about.stats.availability);
        this.updateElement('[data-translate="about.stats.availability_label"]', t.about.stats.availability_label);
        this.updateElement('[data-translate="about.stats.quality"]', t.about.stats.quality);
        this.updateElement('[data-translate="about.stats.quality_label"]', t.about.stats.quality_label);

        // Update pillars
        this.updateElement('[data-translate="about.pillars_title"]', t.about.pillars_title);
        this.updateElement('[data-translate="about.vision.title"]', t.about.vision.title);
        this.updateElement('[data-translate="about.vision.content"]', t.about.vision.content, true);
        this.updateElement('[data-translate="about.mission.title"]', t.about.mission.title);
        this.updateElement('[data-translate="about.mission.content"]', t.about.mission.content, true);

        // Update values
        this.updateElement('[data-translate="about.values_title"]', t.about.values_title);
        this.updateElement('[data-translate="about.values.respect.title"]', t.about.values.respect.title);
        this.updateElement('[data-translate="about.values.respect.content"]', t.about.values.respect.content);
        this.updateElement('[data-translate="about.values.commitment.title"]', t.about.values.commitment.title);
        this.updateElement('[data-translate="about.values.commitment.content"]', t.about.values.commitment.content);
        this.updateElement('[data-translate="about.values.honesty.title"]', t.about.values.honesty.title);
        this.updateElement('[data-translate="about.values.honesty.content"]', t.about.values.honesty.content);

        // Update objectives
        this.updateElement('[data-translate="about.objectives_title"]', t.about.objectives_title);
        this.updateElement('[data-translate="about.objectives.excellence.title"]', t.about.objectives.excellence.title);
        this.updateElement('[data-translate="about.objectives.excellence.content"]', t.about.objectives.excellence.content);
        this.updateElement('[data-translate="about.objectives.innovation.title"]', t.about.objectives.innovation.title);
        this.updateElement('[data-translate="about.objectives.innovation.content"]', t.about.objectives.innovation.content);
        this.updateElement('[data-translate="about.objectives.impact.title"]', t.about.objectives.impact.title);
        this.updateElement('[data-translate="about.objectives.impact.content"]', t.about.objectives.impact.content);

        // Update contact section
        this.updateElement('[data-translate="contact.title"]', t.contact.title);
        this.updateElement('[data-translate="contact.subtitle"]', t.contact.subtitle);
        this.updateElement('[data-translate="contact.location.title"]', t.contact.location.title);
        this.updateElement('[data-translate="contact.location.city"]', t.contact.location.city);
        this.updateElement('[data-translate="contact.location.country"]', t.contact.location.country);
        this.updateElement('[data-translate="contact.phones.title"]', t.contact.phones.title);
        this.updateElement('[data-translate="contact.email.title"]', t.contact.email.title);
        this.updateElement('[data-translate="contact.email.link_text"]', t.contact.email.link_text);

        // Update contact form
        this.updateElement('[data-translate="contact.form.title"]', t.contact.form.title);
        this.updateElement('[data-translate="contact.form.subtitle"]', t.contact.form.subtitle);
        this.updateFormLabelsAndPlaceholders(t.contact.form);

        // Update response time and availability
        this.updateElement('[data-translate="contact.response_time.title"]', t.contact.response_time.title);
        this.updateElement('[data-translate="contact.response_time.content"]', t.contact.response_time.content);
        this.updateElement('[data-translate="contact.availability.title"]', t.contact.availability.title);
        this.updateElement('[data-translate="contact.availability.content"]', t.contact.availability.content);

        // Update footer
        this.updateElement('[data-translate="footer.tagline"]', t.footer.tagline);
        this.updateElement('[data-translate="footer.description"]', t.footer.description);
        this.updateElement('[data-translate="footer.navigation_title"]', t.footer.navigation_title);
        this.updateElement('[data-translate="footer.services_title"]', t.footer.services_title);
        this.updateElement('[data-translate="footer.contact_title"]', t.footer.contact_title);
        this.updateElement('[data-translate="footer.copyright"]', t.footer.copyright);
        this.updateElement('[data-translate="footer.made_with_love"]', t.footer.made_with_love);
    }

    updateElement(selector, content, allowHTML = false) {
        const element = document.querySelector(selector);
        if (element && content) {
            if (allowHTML) {
                element.innerHTML = content;
            } else {
                element.textContent = content;
            }
        }
    }

    updateFormLabelsAndPlaceholders(formTranslations) {
        // Update form labels
        const nameLabel = document.querySelector('label[for="name"]');
        if (nameLabel) nameLabel.textContent = formTranslations.name;

        const companyLabel = document.querySelector('label[for="company"]');
        if (companyLabel) companyLabel.textContent = formTranslations.company;

        const emailLabel = document.querySelector('label[for="email"]');
        if (emailLabel) emailLabel.textContent = formTranslations.email;

        const phoneLabel = document.querySelector('label[for="phone"]');
        if (phoneLabel) phoneLabel.textContent = formTranslations.phone;

        const serviceLabel = document.querySelector('label[for="service"]');
        if (serviceLabel) serviceLabel.textContent = formTranslations.service;

        const messageLabel = document.querySelector('label[for="message"]');
        if (messageLabel) messageLabel.textContent = formTranslations.message;

        // Update placeholders
        const messageTextarea = document.getElementById('message');
        if (messageTextarea) messageTextarea.placeholder = formTranslations.message_placeholder;

        // Update service options
        const serviceSelect = document.getElementById('service');
        if (serviceSelect) {
            const options = serviceSelect.querySelectorAll('option');
            if (options.length > 0) {
                options[0].textContent = formTranslations.service_placeholder;
                if (options[1]) options[1].textContent = formTranslations.service_options.air_charter;
                if (options[2]) options[2].textContent = formTranslations.service_options.quality_assurance;
                if (options[3]) options[3].textContent = formTranslations.service_options.universal_essentials;
                if (options[4]) options[4].textContent = formTranslations.service_options.consultation;
            }
        }

        // Update submit button
        const submitBtn = document.querySelector('.submit-btn span');
        if (submitBtn) submitBtn.textContent = formTranslations.submit;
    }

    updateLanguageToggle() {
        const languageToggle = document.getElementById('languageToggle');
        const languageOptions = document.querySelectorAll('.language-option');
        
        if (languageToggle) {
            languageToggle.innerHTML = `
                <span>${this.currentLanguage.toUpperCase()}</span>
                <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M7 10l5 5 5-5z"/>
                </svg>
            `;
        }

        // Update active language option
        languageOptions.forEach(option => {
            option.classList.toggle('active', option.dataset.lang === this.currentLanguage);
        });
    }

    updateMetaTags() {
        const t = this.translations[this.currentLanguage];
        if (!t) return;

        // Update title
        document.title = t.meta.title;

        // Update meta description
        const metaDescription = document.querySelector('meta[name="description"]');
        if (metaDescription) {
            metaDescription.setAttribute('content', t.meta.description);
        }

        // Update html lang attribute
        document.documentElement.setAttribute('lang', this.currentLanguage);
    }

    setupAnimations() {
        // Intersection Observer for fade-in animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in-up');
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);

        // Observe all elements that should animate
        document.querySelectorAll('.service-card, .company-story, .value-card-enhanced, .core-values, .objectives-section, .contact-card-new, .contact-form-section').forEach(el => {
            observer.observe(el);
        });
    }

    handleNavbarScroll() {
        const navbar = document.querySelector('.navbar');
        if (navbar) {
            if (window.scrollY > 100) {
                navbar.style.background = 'rgba(255, 255, 255, 0.98)';
                navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
            } else {
                navbar.style.background = 'rgba(255, 255, 255, 0.95)';
                navbar.style.boxShadow = 'none';
            }
        }
    }

    async handleFormSubmission(e) {
        e.preventDefault();
        
        const submitBtn = e.target.querySelector('.submit-btn');
        const originalText = submitBtn.innerHTML;
        
        // Show loading state
        submitBtn.innerHTML = `
            <span>Enviando...</span>
            <div class="loading-dot"></div>
            <div class="loading-dot"></div>
            <div class="loading-dot"></div>
        `;
        submitBtn.disabled = true;

        // Simulate form submission (replace with actual form handling)
        setTimeout(() => {
            alert(this.currentLanguage === 'pt' ? 
                'Obrigado! Entraremos em contacto em breve.' : 
                'Thank you! We will contact you soon.');
            
            // Reset form
            e.target.reset();
            
            // Reset button
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 2000);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new AnchorpointApp();
});
