# Anchorpoint - Website Multilíngue

Um website moderno e responsivo para a Anchorpoint, empresa de soluções logísticas em Moçambique, com suporte completo para português e inglês.

## 🚀 Características

### ✨ Design Moderno
- Interface limpa e profissional
- Animações suaves e interativas
- Design responsivo para todos os dispositivos
- Paleta de cores corporativa consistente

### 🌍 Sistema Multilíngue
- Suporte completo para português e inglês
- Troca de idioma dinâmica sem recarregar a página
- Persistência da preferência de idioma no localStorage
- Traduções organizadas em arquivos JSON separados

### 📱 Responsividade
- Layout adaptativo para desktop, tablet e mobile
- Navegação otimizada para dispositivos móveis
- Imagens e conteúdo otimizados para diferentes tamanhos de tela

### 🎯 SEO Otimizado
- Meta tags apropriadas para ambos os idiomas
- Estrutura HTML semântica
- Open Graph e Twitter Cards
- URLs amigáveis e estrutura de navegação clara

## 📁 Estrutura do Projeto

```
anchorpoint-website/
├── index.html                 # Página principal otimizada
├── anchorpoint_landing.html   # Arquivo original (backup)
├── README.md                  # Documentação do projeto
└── assets/
    ├── css/
    │   └── styles.css         # Estilos principais
    ├── js/
    │   └── main.js           # JavaScript principal com sistema de tradução
    ├── images/               # Imagens e ícones
    └── translations/
        ├── pt.json          # Traduções em português
        └── en.json          # Traduções em inglês
```

## 🛠️ Tecnologias Utilizadas

- **HTML5** - Estrutura semântica moderna
- **CSS3** - Estilos avançados com variáveis CSS e Flexbox/Grid
- **JavaScript ES6+** - Funcionalidades interativas e sistema de tradução
- **JSON** - Armazenamento das traduções
- **Google Fonts** - Tipografia Inter para melhor legibilidade

## 🎨 Paleta de Cores

```css
:root {
    --primary-navy: #1a237e;      /* Azul principal */
    --primary-dark: #0d1654;      /* Azul escuro */
    --accent-pink: #e91e63;       /* Rosa de destaque */
    --accent-pink-light: #f48fb1; /* Rosa claro */
    --white: #ffffff;             /* Branco */
    --light-gray: #f5f7fa;        /* Cinza claro */
    --text-primary: #0f172a;      /* Texto principal */
    --text-secondary: #475569;    /* Texto secundário */
}
```

## 🚀 Como Usar

### 1. Instalação
```bash
# Clone ou baixe os arquivos do projeto
# Não há dependências externas - funciona diretamente no navegador
```

### 2. Execução Local
```bash
# Opção 1: Abrir diretamente no navegador
# Abra o arquivo index.html no seu navegador

# Opção 2: Servidor local (recomendado para desenvolvimento)
# Python 3
python -m http.server 8000

# Python 2
python -m SimpleHTTPServer 8000

# Node.js (se tiver o http-server instalado)
npx http-server

# Acesse http://localhost:8000
```

### 3. Personalização

#### Adicionar Novo Idioma
1. Crie um novo arquivo JSON em `assets/translations/` (ex: `fr.json`)
2. Copie a estrutura de `pt.json` e traduza o conteúdo
3. Adicione a opção no HTML:
```html
<button class="language-option" data-lang="fr">Français</button>
```

#### Modificar Conteúdo
- **Textos**: Edite os arquivos JSON em `assets/translations/`
- **Estilos**: Modifique `assets/css/styles.css`
- **Funcionalidades**: Edite `assets/js/main.js`

## 📋 Funcionalidades Implementadas

### ✅ Navegação
- [x] Menu de navegação fixo com efeito de scroll
- [x] Links de navegação suave entre seções
- [x] Seletor de idioma com dropdown animado

### ✅ Seções do Website
- [x] Hero section com call-to-actions
- [x] Seção de serviços com cards interativos
- [x] Seção sobre a empresa com história e valores
- [x] Formulário de contato funcional
- [x] Footer completo com informações da empresa

### ✅ Interatividade
- [x] Animações de entrada (fade-in-up)
- [x] Efeitos hover em cards e botões
- [x] Formulário com validação e feedback
- [x] Sistema de tradução dinâmica

### ✅ Responsividade
- [x] Layout adaptativo para mobile
- [x] Menu mobile otimizado
- [x] Formulário responsivo
- [x] Imagens e textos otimizados

## 🔧 Configurações Avançadas

### Modificar Animações
As animações podem ser personalizadas no CSS:
```css
.fade-in-up {
    animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
```

### Adicionar Novas Seções
1. Adicione o HTML na estrutura apropriada
2. Inclua os atributos `data-translate` para textos
3. Adicione as traduções nos arquivos JSON
4. Estilize no CSS se necessário

### Integração com Backend
Para conectar o formulário a um backend:
1. Modifique a função `handleFormSubmission` em `main.js`
2. Substitua a simulação por uma chamada real à API
3. Adicione tratamento de erros apropriado

## 📱 Compatibilidade

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+
- ✅ iOS Safari 12+
- ✅ Android Chrome 60+

## 🤝 Contribuição

Para contribuir com melhorias:
1. Faça um fork do projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Abra um Pull Request

## 📄 Licença

Este projeto é propriedade da Anchorpoint. Todos os direitos reservados.

## 📞 Suporte

Para suporte técnico ou dúvidas sobre o website:
- Email: <EMAIL>
- Telefone: +258 840 600 224

---

**Desenvolvido com ❤️ para a Anchorpoint - Soluções Logísticas de Confiança**
